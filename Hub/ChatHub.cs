using ChatApp.Backend.Models;

namespace ChatApp.Backend.Hub;
using Microsoft.AspNetCore.SignalR;
public class ChatHub:Hub

{
    public async Task JoinChat(UserConnection userConnection)
    {
        await Clients.All.SendAsync("ReceiveChat", "admin" ,$"{userConnection.UserName} has joined the chat");
    }

    public async Task JoinSpecificChatRoom(UserConnection userConnection)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, userConnection.UserName);
        await Clients.Group(userConnection.UserName).SendAsync("ReceiveChat","admin" ,userConnection.UserName);
    }

}